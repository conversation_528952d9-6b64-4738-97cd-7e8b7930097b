import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:echoverse/services/bible_navigation_service.dart';
import 'package:echoverse/models/bible_book.dart';
import 'package:echoverse/models/bible_verse.dart';

/// Test utilities for Bible navigation testing
class TestHelpers {
  /// Sets up mock SharedPreferences for testing
  static void setupMockSharedPreferences([Map<String, Object>? values]) {
    SharedPreferences.setMockInitialValues(values ?? {});
  }

  /// Creates a test BibleNavigationService
  static Future<BibleNavigationService> createTestNavigationService() async {
    final service = BibleNavigationService();
    await service.initialize();
    return service;
  }

  /// Creates test Bible books
  static List<BibleBook> createTestBooks() {
    return [
      BibleBook(id: 1, name: 'Genesis', testament: 1),
      BibleBook(id: 2, name: 'Exodus', testament: 1),
      BibleBook(id: 3, name: '<PERSON><PERSON>', testament: 1),
      BibleBook(id: 40, name: '<PERSON>', testament: 2),
      Bible<PERSON><PERSON>(id: 41, name: '<PERSON>', testament: 2),
      <PERSON><PERSON><PERSON>(id: 42, name: '<PERSON>', testament: 2),
    ];
  }

  /// Creates test Bible verses
  static List<BibleVerse> createTestVerses(int bookId, int chapter) {
    return List.generate(10, (index) => BibleVerse(
      id: '${bookId}_${chapter}_${index + 1}',
      bookId: bookId,
      chapter: chapter,
      verse: index + 1,
      text: 'This is verse ${index + 1} of chapter $chapter.',
    ));
  }

  /// Creates test reading history
  static List<BibleLocation> createTestReadingHistory() {
    final now = DateTime.now();
    return [
      BibleLocation(
        bookId: 1,
        bookName: 'Genesis',
        chapter: 1,
        timestamp: now,
      ),
      BibleLocation(
        bookId: 1,
        bookName: 'Genesis',
        chapter: 2,
        timestamp: now.subtract(const Duration(hours: 1)),
      ),
      BibleLocation(
        bookId: 40,
        bookName: 'Matthew',
        chapter: 1,
        timestamp: now.subtract(const Duration(days: 1)),
      ),
    ];
  }

  /// Pumps a widget with Material app wrapper
  static Future<void> pumpWidgetWithMaterial(
    WidgetTester tester,
    Widget widget, {
    ThemeData? theme,
  }) async {
    await tester.pumpWidget(
      MaterialApp(
        theme: theme,
        home: widget,
      ),
    );
  }

  /// Waits for all animations and async operations to complete
  static Future<void> waitForAnimations(WidgetTester tester) async {
    await tester.pumpAndSettle();
    await tester.pump(const Duration(milliseconds: 100));
  }

  /// Finds a widget by its text content
  static Finder findByText(String text) {
    return find.text(text);
  }

  /// Finds a widget by its type
  static Finder findByType<T extends Widget>() {
    return find.byType(T);
  }

  /// Taps a widget and waits for animations
  static Future<void> tapAndWait(WidgetTester tester, Finder finder) async {
    await tester.tap(finder);
    await waitForAnimations(tester);
  }

  /// Scrolls a widget and waits for animations
  static Future<void> scrollAndWait(
    WidgetTester tester,
    Finder finder,
    Offset offset,
  ) async {
    await tester.drag(finder, offset);
    await waitForAnimations(tester);
  }

  /// Enters text into a text field
  static Future<void> enterText(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    await tester.enterText(finder, text);
    await waitForAnimations(tester);
  }

  /// Verifies that a widget exists
  static void expectWidgetExists(Finder finder) {
    expect(finder, findsOneWidget);
  }

  /// Verifies that a widget does not exist
  static void expectWidgetNotExists(Finder finder) {
    expect(finder, findsNothing);
  }

  /// Verifies that multiple widgets exist
  static void expectWidgetsExist(Finder finder, int count) {
    expect(finder, findsNWidgets(count));
  }

  /// Creates a test navigation context
  static NavigationContext getTestNavigationContext() {
    return NavigationContext.directNavigation;
  }

  /// Creates test user preferences
  static Map<String, dynamic> createTestUserPreferences() {
    return {
      'autoRestore': true,
      'maxHistoryItems': 50,
      'fontSize': 1.0,
      'theme': 'dark',
    };
  }

  /// Simulates app restart by creating new service instance
  static Future<BibleNavigationService> simulateAppRestart(
    BibleNavigationService oldService,
  ) async {
    oldService.dispose();
    return await createTestNavigationService();
  }

  /// Creates test reading statistics
  static Map<String, dynamic> createTestReadingStatistics() {
    return {
      'chaptersToday': 5,
      'chaptersThisWeek': 20,
      'chaptersThisMonth': 80,
      'uniqueBooksThisMonth': 10,
      'totalChaptersRead': 500,
      'currentStreak': 7,
    };
  }

  /// Verifies navigation service state
  static void verifyNavigationServiceState(
    BibleNavigationService service, {
    bool? isInitialized,
    int? historyLength,
    BibleLocation? currentLocation,
  }) {
    if (isInitialized != null) {
      expect(service.isInitialized, equals(isInitialized));
    }
    if (historyLength != null) {
      expect(service.readingHistory.length, equals(historyLength));
    }
    if (currentLocation != null) {
      expect(service.currentLocation, equals(currentLocation));
    }
  }

  /// Creates a test bookmark
  static Map<String, dynamic> createTestBookmark() {
    return {
      'bookId': 1,
      'bookName': 'Genesis',
      'chapter': 1,
      'scrollPosition': 0.0,
      'timestamp': DateTime.now().toIso8601String(),
      'note': 'Test bookmark',
      'createdAt': DateTime.now().toIso8601String(),
    };
  }

  /// Disposes test resources
  static void disposeTestResources(List<dynamic> resources) {
    for (final resource in resources) {
      if (resource is BibleNavigationService) {
        resource.dispose();
      }
    }
  }
}

/// Custom matchers for testing
class CustomMatchers {
  /// Matches a BibleLocation with specific properties
  static Matcher isBibleLocation({
    int? bookId,
    String? bookName,
    int? chapter,
    double? scrollPosition,
  }) {
    return predicate<BibleLocation>((location) {
      if (bookId != null && location.bookId != bookId) return false;
      if (bookName != null && location.bookName != bookName) return false;
      if (chapter != null && location.chapter != chapter) return false;
      if (scrollPosition != null && location.scrollPosition != scrollPosition) return false;
      return true;
    });
  }

  /// Matches reading statistics with specific values
  static Matcher hasReadingStats({
    int? chaptersToday,
    int? currentStreak,
    int? totalChapters,
  }) {
    return predicate<Map<String, dynamic>>((stats) {
      if (chaptersToday != null && stats['chaptersToday'] != chaptersToday) return false;
      if (currentStreak != null && stats['currentStreak'] != currentStreak) return false;
      if (totalChapters != null && stats['totalChaptersRead'] != totalChapters) return false;
      return true;
    });
  }
}

/// Test data constants
class TestData {
  static const String sampleVerseText = 'In the beginning God created the heavens and the earth.';
  static const String sampleBookName = 'Genesis';
  static const int sampleBookId = 1;
  static const int sampleChapter = 1;
  static const double sampleScrollPosition = 100.0;
}
