import 'package:flutter/material.dart';
import 'package:echoverse/models/bible_book.dart';
import 'package:echoverse/services/bible_service.dart';
import 'package:echoverse/screens/bible_reader_screen.dart';
import 'package:echoverse/utils/storage_helper.dart';

class BibleChaptersScreen extends StatefulWidget {
  final BibleBook book;

  const BibleChaptersScreen({super.key, required this.book});

  @override
  State<BibleChaptersScreen> createState() => _BibleChaptersScreenState();
}

class _BibleChaptersScreenState extends State<BibleChaptersScreen> {
  final BibleService _bibleService = BibleService();
  int _chapterCount = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadChapterCount();
  }

  Future<void> _loadChapterCount() async {
    final count = await _bibleService.getChapterCount(widget.book.id);
    setState(() {
      _chapterCount = count;
      _isLoading = false;
    });
  }

  void _navigateToReader(int chapter) async {
    // Save the current position before navigating
    await StorageHelper.saveLastBibleLocation(widget.book.id, chapter);

    if (!mounted) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BibleReaderScreen(book: widget.book, chapter: chapter),
      ),
    );
  }

  Widget _buildEnhancedChapterCard(int chapter, ThemeData theme) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _navigateToReader(chapter),
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.surface,
                theme.colorScheme.surface.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.12),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
              BoxShadow(
                color: theme.colorScheme.primary.withValues(alpha: 0.04),
                blurRadius: 16,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Background pattern
              Positioned(
                top: -10,
                right: -10,
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
              ),
              // Chapter number
              Center(
                child: Text(
                  chapter.toString().padLeft(2, '0'),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                    letterSpacing: -0.5,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator(color: theme.colorScheme.primary))
          : Column(
              children: [
                // Book Selector Pill (like in the image)
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 20, 20),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                _getBookAbbreviation(widget.book.name),
                                style: theme.textTheme.labelLarge?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              widget.book.name,
                              style: theme.textTheme.titleLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        Icon(
                          Icons.expand_more,
                          color: Colors.white.withValues(alpha: 0.9),
                          size: 24,
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Enhanced Chapter Grid
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: theme.brightness == Brightness.dark
                          ? theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.6)
                          : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.4),
                      borderRadius: BorderRadius.circular(28),
                      border: Border.all(
                        color: theme.colorScheme.outline.withValues(alpha: 0.08),
                        width: 1.5,
                      ),
                    ),
                    child: GridView.builder(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        childAspectRatio: 1.0,
                      ),
                      itemCount: _chapterCount,
                      itemBuilder: (context, index) {
                        final chapter = index + 1;
                        return _buildEnhancedChapterCard(chapter, theme);
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
    );
  }

  String _getBookAbbreviation(String bookName) {
    // Generate abbreviation (first letter of each word or first 2-3 letters)
    if (bookName.contains(' ')) {
      return bookName.split(' ').map((word) => word[0]).join().toUpperCase();
    }
    return bookName.substring(0, bookName.length > 3 ? 3 : bookName.length).toUpperCase();
  }
}
