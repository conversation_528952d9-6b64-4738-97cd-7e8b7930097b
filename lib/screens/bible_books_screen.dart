import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:echoverse/models/bible_book.dart';
import 'package:echoverse/models/user.dart';
import 'package:echoverse/services/bible_service.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/user_service.dart';
import 'package:echoverse/services/bible_navigation_service.dart';
import 'package:echoverse/screens/bible_chapters_screen.dart';
import 'package:echoverse/screens/bible_reader_screen.dart';
import 'package:echoverse/design_system/app_spacing.dart';

class BibleBooksScreen extends StatefulWidget {
  final ProgressService? progressService;
  final UserService? userService;
  final BibleNavigationService? navigationService;

  const BibleBooksScreen({
    super.key,
    this.progressService,
    this.userService,
    this.navigationService,
  });

  @override
  State<BibleBooksScreen> createState() => _BibleBooksScreenState();
}

class _BibleBooksScreenState extends State<BibleBooksScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final BibleService _bibleService = BibleService();
  final TextEditingController _searchController = TextEditingController();
  late final BibleNavigationService _navigationService;
  List<BibleBook> _oldTestamentBooks = [];
  List<BibleBook> _newTestamentBooks = [];
  List<BibleBook> _filteredOldTestamentBooks = [];
  List<BibleBook> _filteredNewTestamentBooks = [];
  bool _isLoading = true;
  bool _isSearching = false;
  String? _error;
  String _searchQuery = '';
  late TabController _tabController;
  final NavigationContext _currentNavigationContext = NavigationContext.tabSwitch;
  User? _currentUser;
  Map<int, int> _bookProgressCounts = {}; // bookId -> number of verses practiced
  Timer? _searchDebounceTimer;
  List<BibleBook> _recentlyReadBooks = [];
  bool _showRecentlyRead = false;
  bool _hasInitializedData = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _navigationService = widget.navigationService ?? BibleNavigationService();
    _tabController = TabController(length: 2, vsync: this);
    // Don't call async methods in initState
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Now it's safe to access Theme.of(context) and other inherited widgets
    if (!_hasInitializedData) {
      _hasInitializedData = true;
      _initializeServices();
      _loadBooks();
      _loadUserData();
    }
  }

  Future<void> _initializeServices() async {
    await _navigationService.initialize();
    // Start navigation session to track context
    await _navigationService.startNavigationSession(_currentNavigationContext);

    // Listen to navigation service changes - but only after widget is fully initialized
    if (mounted) {
      _navigationService.addListener(_onNavigationStateChanged);
    }
  }

  void _onNavigationStateChanged() {
    // Update UI when navigation state changes (debounced)
    // Only proceed if widget is fully initialized and mounted
    if (mounted && _hasInitializedData && !_isLoading) {
      // Debounce rapid state changes
      Timer(const Duration(milliseconds: 100), () {
        if (mounted) {
          setState(() {
            // Update recently read books based on navigation history
            _updateRecentlyReadBooks();
          });
        }
      });
    }
  }

  void _updateRecentlyReadBooks() {
    try {
      final recentBooks = _navigationService.getRecentBooks(limit: 3);
      final allBooks = [..._oldTestamentBooks, ..._newTestamentBooks];

      // Use a more efficient lookup with a map for better performance
      final bookMap = {for (var book in allBooks) book.id: book};

      _recentlyReadBooks = recentBooks
          .map((location) => bookMap[location.bookId] ?? BibleBook(
                id: location.bookId,
                name: location.bookName,
                testament: location.bookId <= 39 ? 1 : 2,
              ))
          .toList();

      _showRecentlyRead = _recentlyReadBooks.isNotEmpty;
    } catch (e) {
      // Gracefully handle errors without breaking the UI
      debugPrint('Error updating recently read books: $e');
      _showRecentlyRead = false;
    }
  }

  @override
  void dispose() {
    // Clean up navigation service listener
    _navigationService.removeListener(_onNavigationStateChanged);

    // Clean up controllers and timers
    _tabController.dispose();
    _searchController.dispose();
    _searchDebounceTimer?.cancel();

    // Clear large data structures to help with garbage collection
    _oldTestamentBooks.clear();
    _newTestamentBooks.clear();
    _filteredOldTestamentBooks.clear();
    _filteredNewTestamentBooks.clear();
    _recentlyReadBooks.clear();
    _bookProgressCounts.clear();

    super.dispose();
  }

  Future<void> _retryLoadBooks() async {
    // This method is called from UI interactions, so it's safe to show feedback
    if (mounted) {
      _showSnackBar('Retrying...', isSuccess: true);
    }
    await _loadBooks();
    if (mounted && _error == null) {
      _showSnackBar('Bible books loaded successfully', isSuccess: true);
    } else if (mounted && _error != null) {
      _showSnackBar('Failed to load books', isError: true);
    }
  }

  Future<void> _loadBooks() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // This will trigger database download if needed
      final oldTestament = await _bibleService.getOldTestamentBooks();
      final newTestament = await _bibleService.getNewTestamentBooks();

      if (!mounted) return;

      setState(() {
        _oldTestamentBooks = oldTestament;
        _newTestamentBooks = newTestament;
        _filteredOldTestamentBooks = oldTestament;
        _filteredNewTestamentBooks = newTestament;
        _isLoading = false;
      });

      // After loading books, check if we should restore last position
      await _restoreLastPosition();

      // Load progress data if services are available
      await _loadProgressData();
    } catch (e) {
      if (!mounted) return;

      String errorMessage = 'Failed to load Bible books';
      String actionMessage = 'Please try again';

      // Provide more user-friendly error messages with actionable advice
      if (e.toString().contains('download') || e.toString().contains('network')) {
        errorMessage = 'Unable to download Bible database';
        actionMessage = 'Check your internet connection and try again';
      } else if (e.toString().contains('database') || e.toString().contains('SQLite')) {
        errorMessage = 'Database error occurred';
        actionMessage = 'Try restarting the app or clearing app data';
      } else if (e.toString().contains('permission')) {
        errorMessage = 'Storage permission required';
        actionMessage = 'Please grant storage permission in settings';
      } else {
        errorMessage = 'Unexpected error occurred';
        actionMessage = 'Contact support if this persists';
      }

      setState(() {
        _error = '$errorMessage. $actionMessage';
        _isLoading = false;
      });
    }
  }

  Future<void> _restoreLastPosition() async {
    // Check if we should auto-restore based on navigation context
    if (!_navigationService.shouldAutoRestoreForContext(_currentNavigationContext)) {
      return;
    }

    final currentLocation = _navigationService.currentLocation;
    if (currentLocation == null || !mounted) return;

    // Find the book in either testament
    final allBooks = [..._oldTestamentBooks, ..._newTestamentBooks];
    final book = allBooks.firstWhere(
      (b) => b.id == currentLocation.bookId,
      orElse: () => allBooks.first,
    );

    // Show restoration dialog with user choice
    _showRestorationDialog(book, currentLocation);
  }

  void _showRestorationDialog(BibleBook book, BibleLocation location) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.bookmark_outlined,
          color: Theme.of(context).colorScheme.primary,
          size: 32,
        ),
        title: const Text('Continue Reading?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodyMedium,
                children: [
                  const TextSpan(text: 'You were reading '),
                  TextSpan(
                    text: '${book.name} ${location.chapter}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(text: '.'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Would you like to continue from where you left off?',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'You can disable this in settings',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Disable auto-restore for this session
              _navigationService.updateUserPreferences({'autoRestore': false});
              _showSnackBar('Auto-restore disabled for this session', isSuccess: true);
            },
            child: const Text('Not Now'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Stay Here'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToRestoredLocation(book, location);
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  void _navigateToRestoredLocation(BibleBook book, BibleLocation location) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BibleReaderScreen(
          book: book,
          chapter: location.chapter,
          navigationService: _navigationService,
        ),
      ),
    );
  }

  void _showSnackBar(String message, {bool isSuccess = false, bool isError = false}) {
    if (!mounted) return;

    final theme = Theme.of(context);
    Color backgroundColor;
    Color textColor;
    IconData icon;

    if (isError) {
      backgroundColor = theme.colorScheme.errorContainer;
      textColor = theme.colorScheme.onErrorContainer;
      icon = Icons.error_outline;
    } else if (isSuccess) {
      backgroundColor = theme.colorScheme.primaryContainer;
      textColor = theme.colorScheme.onPrimaryContainer;
      icon = Icons.check_circle_outline;
    } else {
      backgroundColor = theme.colorScheme.surfaceContainerHighest;
      textColor = theme.colorScheme.onSurface;
      icon = Icons.info_outline;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: textColor, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(color: textColor),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: isError ? 4 : 3),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: textColor.withValues(alpha: 0.8),
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }



  void _navigateToChapters(BibleBook book) {
    // Haptic feedback for navigation
    HapticFeedback.lightImpact();

    // Update navigation context for chapter selection
    _navigationService.startNavigationSession(NavigationContext.directNavigation);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BibleChaptersScreen(
          book: book,
          navigationService: _navigationService,
        ),
      ),
    );
  }

  void _navigateToRecentBook(BibleBook book) {
    // Haptic feedback for navigation
    HapticFeedback.lightImpact();

    // Get the last read location for this book
    final lastLocation = _navigationService.getLastLocationForBook(book.id);
    if (lastLocation != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BibleReaderScreen(
            book: book,
            chapter: lastLocation.chapter,
            navigationService: _navigationService,
          ),
        ),
      );
    } else {
      // If no last location, go to chapters screen
      _navigateToChapters(book);
    }
  }

  void _onSearchChanged(String query) {
    // Cancel previous timer
    _searchDebounceTimer?.cancel();

    // Set immediate state for query
    setState(() {
      _searchQuery = query;
      _isSearching = query.isNotEmpty;
    });

    // Debounce the actual filtering
    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _performSearch(query);
    });
  }

  void _performSearch(String query) {
    if (!mounted) return;

    // Optimize search with early return and case-insensitive caching
    final lowerQuery = query.toLowerCase();

    setState(() {
      if (query.isEmpty) {
        _filteredOldTestamentBooks = _oldTestamentBooks;
        _filteredNewTestamentBooks = _newTestamentBooks;
      } else {
        // Use more efficient filtering with cached lowercase names
        _filteredOldTestamentBooks = _oldTestamentBooks
            .where((book) => book.name.toLowerCase().contains(lowerQuery))
            .toList();
        _filteredNewTestamentBooks = _newTestamentBooks
            .where((book) => book.name.toLowerCase().contains(lowerQuery))
            .toList();
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    _onSearchChanged('');
  }

  Future<void> _loadUserData() async {
    if (widget.userService != null) {
      try {
        final user = await widget.userService!.getCurrentUser();
        setState(() {
          _currentUser = user;
        });
      } catch (e) {
        // Silently fail if user service is not available
      }
    }
  }

  Future<void> _loadProgressData() async {
    if (widget.progressService != null && _currentUser != null) {
      try {
        final allProgress = widget.progressService!.getAllProgress()
            .where((p) => p.userId == _currentUser!.id)
            .toList();

        // Count verses practiced per book
        final bookCounts = <int, int>{};
        for (final progress in allProgress) {
          // Extract book ID from verse ID (format: bible_bookId_chapter_verse)
          final parts = progress.verseId.split('_');
          if (parts.length >= 2) {
            final bookId = int.tryParse(parts[1]);
            if (bookId != null) {
              bookCounts[bookId] = (bookCounts[bookId] ?? 0) + 1;
            }
          }
        }

        setState(() {
          _bookProgressCounts = bookCounts;
        });

        await _loadRecentlyReadBooks();
      } catch (e) {
        // Silently fail if progress service is not available
      }
    }
  }

  Future<void> _loadRecentlyReadBooks() async {
    // Load recently read books based on progress data
    final recentBooks = <BibleBook>[];
    final allBooks = [..._oldTestamentBooks, ..._newTestamentBooks];

    // Sort books by progress count (most practiced first)
    final sortedBooks = allBooks.where((book) =>
      _bookProgressCounts[book.id] != null && _bookProgressCounts[book.id]! > 0
    ).toList()
      ..sort((a, b) => (_bookProgressCounts[b.id] ?? 0).compareTo(_bookProgressCounts[a.id] ?? 0));

    // Take top 3 most practiced books
    recentBooks.addAll(sortedBooks.take(3));

    setState(() {
      _recentlyReadBooks = recentBooks;
      _showRecentlyRead = recentBooks.isNotEmpty;
    });
  }

  String _getBookCategory(BibleBook book) {
    if (book.isOldTestament) {
      if (book.id <= 5) return 'Law';
      if (book.id <= 17) return 'History';
      if (book.id <= 22) return 'Poetry';
      if (book.id <= 27) return 'Major Prophets';
      return 'Minor Prophets';
    } else {
      if (book.id <= 43) return 'Gospels';
      if (book.id == 44) return 'History';
      if (book.id <= 56) return 'Paul\'s Letters';
      if (book.id <= 65) return 'General Letters';
      return 'Prophecy';
    }
  }

  Color _getCategoryColor(String category, ThemeData theme) {
    switch (category) {
      case 'Law':
        return Colors.purple.withValues(alpha: 0.15);
      case 'History':
        return Colors.blue.withValues(alpha: 0.15);
      case 'Poetry':
        return Colors.pink.withValues(alpha: 0.15);
      case 'Major Prophets':
        return Colors.orange.withValues(alpha: 0.15);
      case 'Minor Prophets':
        return Colors.amber.withValues(alpha: 0.15);
      case 'Gospels':
        return Colors.green.withValues(alpha: 0.15);
      case 'Paul\'s Letters':
        return Colors.indigo.withValues(alpha: 0.15);
      case 'General Letters':
        return Colors.teal.withValues(alpha: 0.15);
      case 'Prophecy':
        return Colors.red.withValues(alpha: 0.15);
      default:
        return theme.colorScheme.primaryContainer.withValues(alpha: 0.3);
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Law':
        return Icons.gavel;
      case 'History':
        return Icons.history_edu;
      case 'Poetry':
        return Icons.music_note;
      case 'Major Prophets':
      case 'Minor Prophets':
        return Icons.campaign;
      case 'Gospels':
        return Icons.auto_stories;
      case 'Paul\'s Letters':
      case 'General Letters':
        return Icons.mail;
      case 'Prophecy':
        return Icons.visibility;
      default:
        return Icons.book;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        leading: Semantics(
          label: 'Go back to previous screen',
          child: IconButton(
            icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
            onPressed: () => Navigator.pop(context),
            tooltip: 'Go back',
          ),
        ),
        title: _isSearching
          ? Semantics(
              label: 'Search Bible books',
              hint: 'Type to search for Bible books by name',
              child: TextField(
                controller: _searchController,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: 'Search books...',
                  border: InputBorder.none,
                  hintStyle: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                onChanged: _onSearchChanged,
              ),
            )
          : Text('Bible', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
        actions: [
          // Connection status indicator
          if (_error?.contains('network') == true)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Tooltip(
                message: 'Offline mode',
                child: Icon(
                  Icons.wifi_off,
                  color: theme.colorScheme.error,
                  size: 20,
                ),
              ),
            ),
          Semantics(
            label: _isSearching ? 'Close search' : 'Search Bible books',
            hint: _isSearching ? 'Tap to close search and return to book list' : 'Tap to search for Bible books',
            child: IconButton(
              icon: Icon(_isSearching ? Icons.close : Icons.search),
              onPressed: () {
                if (_isSearching) {
                  _clearSearch();
                } else {
                  setState(() => _isSearching = true);
                }
              },
              tooltip: _isSearching ? 'Close search' : 'Search books',
            ),
          ),
        ],
        bottom: _isSearching ? null : TabBar(
          controller: _tabController,
          indicatorColor: theme.colorScheme.primary,
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          labelStyle: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          unselectedLabelStyle: theme.textTheme.titleSmall,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'Old Testament'),
            Tab(text: 'New Testament'),
          ],
        ),
      ),
      body: _isLoading
          ? _buildEnhancedLoadingState(theme)
          : _error != null
              ? _buildEnhancedErrorState(theme)
              : _isSearching
                  ? _buildSearchResults()
                  : Column(
                      children: [
                        if (_currentUser != null && widget.progressService != null)
                          _buildProgressOverview(),
                        if (_navigationService.isInitialized)
                          _buildReadingStatistics(),
                        if (_showRecentlyRead)
                          _buildRecentlyReadSection(),
                        Expanded(
                          child: TabBarView(
                            controller: _tabController,
                            children: [
                              _buildBooksList(_filteredOldTestamentBooks),
                              _buildBooksList(_filteredNewTestamentBooks),
                            ],
                          ),
                        ),
                      ],
                    ),
    );
  }

  Widget _buildEnhancedLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated loading indicator
          SizedBox(
            width: 80,
            height: 80,
            child: Stack(
              children: [
                CircularProgressIndicator(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  strokeWidth: 8,
                ),
                Positioned.fill(
                  child: CircularProgressIndicator(
                    color: theme.colorScheme.primary,
                    strokeWidth: 4,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          // Loading text with animation
          Text(
            'Loading Bible database...',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'This may take a moment on first launch',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          // Progress steps
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
            ),
            child: Column(
              children: [
                _buildLoadingStep(theme, Icons.download, 'Downloading Bible data', true),
                const SizedBox(height: AppSpacing.sm),
                _buildLoadingStep(theme, Icons.storage, 'Setting up database', false),
                const SizedBox(height: AppSpacing.sm),
                _buildLoadingStep(theme, Icons.check, 'Ready to use', false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingStep(ThemeData theme, IconData icon, String text, bool isActive) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: isActive
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurface.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 14,
            color: isActive
              ? theme.colorScheme.onPrimary
              : theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        Text(
          text,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isActive
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedErrorState(ThemeData theme) {
    final isNetworkError = _error?.contains('network') == true ||
                          _error?.contains('download') == true;
    final isDatabaseError = _error?.contains('database') == true;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                isNetworkError ? Icons.wifi_off :
                isDatabaseError ? Icons.storage : Icons.error_outline,
                size: 40,
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              isNetworkError ? 'Connection Problem' :
              isDatabaseError ? 'Database Issue' : 'Something Went Wrong',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              _error!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (isNetworkError) ...[
              const SizedBox(height: AppSpacing.md),
              Container(
                padding: const EdgeInsets.all(AppSpacing.md),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.offline_bolt_outlined,
                      color: theme.colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    Text(
                      'Offline Mode Available',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Some features may be limited without internet',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: AppSpacing.xl),
            Wrap(
              spacing: AppSpacing.md,
              runSpacing: AppSpacing.sm,
              alignment: WrapAlignment.center,
              children: [
                OutlinedButton.icon(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('Go Back'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.lg,
                      vertical: AppSpacing.md,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _retryLoadBooks,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Try Again'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.lg,
                      vertical: AppSpacing.md,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressOverview() {
    final theme = Theme.of(context);
    final allProgress = widget.progressService!.getAllProgress()
        .where((p) => p.userId == _currentUser!.id)
        .toList();
    final totalBooksWithProgress = _bookProgressCounts.keys.length;
    final totalVersesStarted = allProgress.length;
    final masteredVerses = allProgress.where((p) => p.isMastered).length;

    return Container(
      margin: const EdgeInsets.all(AppSpacing.md),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Progress',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  theme,
                  Icons.menu_book,
                  'Books Started',
                  '$totalBooksWithProgress/66',
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  theme,
                  Icons.auto_stories,
                  'Verses Started',
                  '$totalVersesStarted',
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  theme,
                  Icons.check_circle,
                  'Mastered',
                  '$masteredVerses',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(ThemeData theme, IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          value,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.labelSmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildReadingStatistics() {
    final theme = Theme.of(context);
    final stats = _navigationService.getReadingStatistics();

    return Container(
      margin: const EdgeInsets.fromLTRB(AppSpacing.md, 0, AppSpacing.md, AppSpacing.md),
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppSpacing.radiusXl),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                'Reading Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.md),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Today',
                  '${stats['chaptersToday']}',
                  'chapters',
                  theme,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildStatCard(
                  'Streak',
                  '${stats['currentStreak']}',
                  'days',
                  theme,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildStatCard(
                  'This Month',
                  '${stats['uniqueBooksThisMonth']}',
                  'books',
                  theme,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, String unit, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            unit,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentlyReadSection() {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.fromLTRB(AppSpacing.md, 0, AppSpacing.md, AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
            child: Row(
              children: [
                Icon(
                  Icons.history,
                  color: theme.colorScheme.secondary,
                  size: 20,
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  'Recently Read',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
              itemCount: _recentlyReadBooks.length,
              itemBuilder: (context, index) {
                final book = _recentlyReadBooks[index];
                return _buildRecentBookCard(book, theme);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentBookCard(BibleBook book, ThemeData theme) {
    final category = _getBookCategory(book);
    final categoryColor = _getCategoryColor(category, theme);
    final progressCount = _bookProgressCounts[book.id] ?? 0;

    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: AppSpacing.md),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToRecentBook(book),
          borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
              border: Border.all(
                color: categoryColor.withValues(alpha: 0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: categoryColor.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: categoryColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          '${book.id}',
                          style: theme.textTheme.labelLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.secondary.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '$progressCount',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.secondary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppSpacing.sm),
                Text(
                  book.name,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  category,
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    final theme = Theme.of(context);
    final allFilteredBooks = [..._filteredOldTestamentBooks, ..._filteredNewTestamentBooks];

    if (_searchQuery.isNotEmpty && allFilteredBooks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'No books found',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Try searching for a different book name',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: allFilteredBooks.length,
      itemBuilder: (context, index) {
        final book = allFilteredBooks[index];
        return _buildEnhancedBookCard(book, theme);
      },
    );
  }

  Widget _buildBooksList(List<BibleBook> books) {
    final theme = Theme.of(context);
    return RefreshIndicator(
      onRefresh: () async {
        await _loadBooks();
        await _loadProgressData();
      },
      color: theme.colorScheme.primary,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppSpacing.md),
        itemCount: books.length,
        itemBuilder: (context, index) {
          final book = books[index];
          return _buildEnhancedBookCard(book, theme);
        },
      ),
    );
  }

  Widget _buildEnhancedBookCard(BibleBook book, ThemeData theme) {
    final category = _getBookCategory(book);
    final categoryColor = _getCategoryColor(category, theme);
    final categoryIcon = _getCategoryIcon(category);
    final progressCount = _bookProgressCounts[book.id] ?? 0;
    final hasProgress = progressCount > 0;

    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.md),
      child: Semantics(
        label: '${book.name}, book ${book.id}, $category category, ${book.isOldTestament ? 'Old Testament' : 'New Testament'}',
        hint: 'Tap to view chapters in ${book.name}',
        button: true,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _navigateToChapters(book),
            borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
            child: Container(
            padding: const EdgeInsets.all(AppSpacing.lg),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(AppSpacing.radiusXl),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.08),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.04),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.02),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Row(
              children: [
                // Enhanced book number and category icon
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        categoryColor,
                        categoryColor.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
                    boxShadow: [
                      BoxShadow(
                        color: categoryColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Text(
                          '${book.id}',
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 6,
                        right: 6,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Icon(
                            categoryIcon,
                            size: 12,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                // Book info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        book.name,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          letterSpacing: -0.3,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSpacing.sm,
                              vertical: AppSpacing.xs,
                            ),
                            decoration: BoxDecoration(
                              color: categoryColor,
                              borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                            ),
                            child: Text(
                              category,
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(width: AppSpacing.sm),
                          Text(
                            book.isOldTestament ? 'OT' : 'NT',
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                          if (hasProgress) ...[
                            const SizedBox(width: AppSpacing.sm),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpacing.sm,
                                vertical: AppSpacing.xs,
                              ),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.secondary.withValues(alpha: 0.15),
                                borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    size: 12,
                                    color: theme.colorScheme.secondary,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    '$progressCount',
                                    style: theme.textTheme.labelSmall?.copyWith(
                                      color: theme.colorScheme.secondary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                // Arrow icon
                Icon(
                  Icons.chevron_right,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
                  size: 24,
                ),
              ],
            ),
          ),
        ),
      ),
      ),
    );
  }
}
