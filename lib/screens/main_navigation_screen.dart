import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:echoverse/services/settings_service.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/verse_service.dart';
import 'package:echoverse/services/user_service.dart';
import 'package:echoverse/services/auth_service.dart';
import 'package:echoverse/services/bible_navigation_service.dart';
import 'package:echoverse/screens/home_tab.dart';
import 'package:echoverse/screens/bible_books_screen.dart';
import 'package:echoverse/screens/practice_tab.dart';
import 'package:echoverse/screens/saved_tab.dart';
import 'package:echoverse/screens/profile_tab.dart';
import 'package:echoverse/design_system/app_spacing.dart';

class MainNavigationScreen extends StatefulWidget {
  final SettingsService settingsService;

  const MainNavigationScreen({super.key, required this.settingsService});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen>
    with TickerProviderStateMixin {
  int _selectedIndex = 0;

  // Initialize services
  late final ProgressService _progressService;
  late final VerseService _verseService;
  late final UserService _userService;
  late final BibleNavigationService _bibleNavigationService;
  final AuthService _authService = AuthService();

  // Animation controllers
  late AnimationController _tabAnimationController;
  late Animation<double> _tabAnimation;
  late AnimationController _badgeAnimationController;
  late Animation<double> _badgeAnimation;

  // Notification badges
  Map<int, int> _notificationCounts = {};
  Map<int, String> _notificationTypes = {}; // 'count', 'dot', 'new'

  // Loading states
  Map<int, bool> _tabLoadingStates = {};

  // Focus management
  final List<FocusNode> _focusNodes = [];

  @override
  void initState() {
    super.initState();
    _progressService = ProgressService();
    _verseService = VerseService();
    _userService = UserService();
    _bibleNavigationService = BibleNavigationService();

    // Initialize focus nodes for each tab
    for (int i = 0; i < 5; i++) {
      _focusNodes.add(FocusNode());
    }

    // Initialize animations
    _tabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _tabAnimation = CurvedAnimation(
      parent: _tabAnimationController,
      curve: Curves.easeInOut,
    );

    _badgeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _badgeAnimation = CurvedAnimation(
      parent: _badgeAnimationController,
      curve: Curves.elasticOut,
    );

    _initializeServices();
    _loadNotificationCounts();
  }

  @override
  void dispose() {
    _tabAnimationController.dispose();
    _badgeAnimationController.dispose();
    for (final focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  Future<void> _initializeServices() async {
    await _verseService.initialize();
    await _progressService.initialize();
    await _bibleNavigationService.initialize();

    // Set current user for Firestore sync
    final currentUser = _authService.currentFirebaseUser;
    if (currentUser != null) {
      await _progressService.setCurrentUser(currentUser.uid);
      await _verseService.setCurrentUser(currentUser.uid);
    }
  }

  Future<void> _loadNotificationCounts() async {
    // Load notification counts for each tab
    try {
      final currentUser = _authService.currentFirebaseUser;
      if (currentUser != null) {
        // Calculate notification counts based on app data
        final allProgress = _progressService.getAllProgress()
            .where((p) => p.userId == currentUser.uid)
            .toList();

        // Home tab: verses due for review
        final versesForReview = allProgress.where((p) =>
          DateTime.now().difference(p.lastPracticed).inDays >= 1
        ).length;

        // Saved tab: favorite verses count
        final favoriteVerses = _verseService.getFavoriteVerses();

        setState(() {
          _notificationCounts = {
            0: versesForReview, // Home
            3: favoriteVerses.length, // Saved
          };
          _notificationTypes = {
            0: versesForReview > 0 ? 'count' : 'none',
            1: 'dot', // Bible - always show a dot for new content
            2: 'new', // Practice - show "new" indicator
            3: favoriteVerses.isNotEmpty ? 'count' : 'none',
          };
        });

        // Animate badges if there are notifications
        if (versesForReview > 0 || favoriteVerses.isNotEmpty) {
          _badgeAnimationController.forward();
        }
      }
    } catch (e) {
      // Silently fail if services aren't ready
    }
  }

  void _onItemTapped(int index) {
    if (index == _selectedIndex) return;

    // Haptic feedback
    HapticFeedback.lightImpact();

    // Update navigation context for Bible tab
    if (index == 1) { // Bible tab
      _bibleNavigationService.startNavigationSession(NavigationContext.tabSwitch);
    }

    // Set loading state for the new tab
    setState(() {
      _tabLoadingStates[index] = true;
      _selectedIndex = index;
    });

    // Trigger animation
    _tabAnimationController.forward().then((_) {
      _tabAnimationController.reset();
    });

    // Simulate tab content loading and clear loading state
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          _tabLoadingStates[index] = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: _buildOptimizedTabView(),
      bottomNavigationBar: _buildEnhancedBottomNavigation(theme),
    );
  }

  Widget _buildOptimizedTabView() {
    return IndexedStack(
      index: _selectedIndex,
      children: [
        // Home Tab
        HomeTab(
          settingsService: widget.settingsService,
          progressService: _progressService,
          verseService: _verseService,
          userService: _userService,
        ),
        // Bible Tab
        BibleBooksScreen(
          progressService: _progressService,
          userService: _userService,
          navigationService: _bibleNavigationService,
        ),
        // Practice Tab
        PracticeTab(
          settingsService: widget.settingsService,
          progressService: _progressService,
          userService: _userService,
        ),
        // Saved Tab
        SavedTab(
          progressService: _progressService,
          verseService: _verseService,
          userService: _userService,
        ),
        // Profile Tab
        ProfileTab(settingsService: widget.settingsService),
      ],
    );
  }

  Widget _buildEnhancedBottomNavigation(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, -8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.05),
            blurRadius: 40,
            offset: const Offset(0, -12),
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Container(
          height: AppSpacing.bottomNavHeight,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.md,
            vertical: AppSpacing.xs,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNavItem(
                theme,
                0,
                Icons.home_outlined,
                Icons.home,
                'Home',
              ),
              _buildNavItem(
                theme,
                1,
                Icons.menu_book_outlined,
                Icons.menu_book,
                'Bible',
              ),
              _buildNavItem(
                theme,
                2,
                Icons.mic_outlined,
                Icons.mic,
                'Practice',
              ),
              _buildNavItem(
                theme,
                3,
                Icons.star_outline,
                Icons.star,
                'Saved',
              ),
              _buildNavItem(
                theme,
                4,
                Icons.person_outline,
                Icons.person,
                'Profile',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    ThemeData theme,
    int index,
    IconData unselectedIcon,
    IconData selectedIcon,
    String label,
  ) {
    final isSelected = _selectedIndex == index;
    final notificationCount = _notificationCounts[index] ?? 0;
    final notificationType = _notificationTypes[index] ?? 'none';
    final hasNotification = notificationType != 'none';
    final isLoading = _tabLoadingStates[index] ?? false;

    return Expanded(
      flex: 1,
      child: Semantics(
        label: '$label tab',
        hint: isSelected ? 'Currently selected' : 'Tap to switch to $label',
        selected: isSelected,
        button: true,
        child: Material(
          color: Colors.transparent,
          child: Focus(
            focusNode: _focusNodes[index],
            onKeyEvent: (node, event) {
              // Handle keyboard navigation
              if (event.logicalKey.keyLabel == 'Enter' ||
                  event.logicalKey.keyLabel == 'Space') {
                _onItemTapped(index);
                return KeyEventResult.handled;
              }
              return KeyEventResult.ignored;
            },
            child: Builder(
              builder: (context) {
                final isFocused = Focus.of(context).hasFocus;
                return InkWell(
                  onTap: () => _onItemTapped(index),
                  borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
                  focusColor: theme.colorScheme.primary.withValues(alpha: 0.1),
                  child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              constraints: const BoxConstraints(
                maxHeight: 64, // Ensure it fits within available space
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.xs,
                vertical: AppSpacing.xs,
              ),
              decoration: BoxDecoration(
                color: isSelected
                    ? theme.colorScheme.primaryContainer.withValues(alpha: 0.8)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
                border: isFocused
                    ? Border.all(
                        color: theme.colorScheme.primary,
                        width: 2,
                      )
                    : null,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon with notification badge and loading indicator
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        child: isLoading
                            ? SizedBox(
                                width: 22,
                                height: 22,
                                child: CircularProgressIndicator(
                                  key: ValueKey('$index-loading'),
                                  strokeWidth: 2,
                                  color: theme.colorScheme.primary,
                                ),
                              )
                            : Icon(
                                isSelected ? selectedIcon : unselectedIcon,
                                key: ValueKey('$index-$isSelected'),
                                size: 22,
                                color: isSelected
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                      ),
                      // Notification badge
                      if (hasNotification && !isLoading)
                        Positioned(
                          right: -4,
                          top: -4,
                          child: _buildNotificationIndicator(theme, notificationType, notificationCount),
                        ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  // Label
                  Flexible(
                    child: AnimatedDefaultTextStyle(
                      duration: const Duration(milliseconds: 200),
                      style: theme.textTheme.labelSmall!.copyWith(
                        color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        fontSize: 11,
                      ),
                      child: Text(
                        label,
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIndicator(ThemeData theme, String type, int count) {
    return AnimatedBuilder(
      animation: _badgeAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (_badgeAnimation.value * 0.2),
          child: _buildIndicatorByType(theme, type, count),
        );
      },
    );
  }

  Widget _buildIndicatorByType(ThemeData theme, String type, int count) {
    switch (type) {
      case 'count':
        return _buildCountBadge(theme, count);
      case 'dot':
        return _buildDotIndicator(theme);
      case 'new':
        return _buildNewIndicator(theme);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildCountBadge(ThemeData theme, int count) {
    return Container(
      constraints: const BoxConstraints(
        minWidth: 16,
        minHeight: 16,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: theme.colorScheme.error,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.surface,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.error.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        count > 99 ? '99+' : count.toString(),
        style: theme.textTheme.labelSmall?.copyWith(
          color: theme.colorScheme.onError,
          fontWeight: FontWeight.bold,
          fontSize: 10,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildDotIndicator(ThemeData theme) {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: theme.colorScheme.surface,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }

  Widget _buildNewIndicator(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondary,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: theme.colorScheme.surface,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.secondary.withValues(alpha: 0.3),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Text(
        'NEW',
        style: theme.textTheme.labelSmall?.copyWith(
          color: theme.colorScheme.onSecondary,
          fontWeight: FontWeight.bold,
          fontSize: 8,
        ),
      ),
    );
  }
}

